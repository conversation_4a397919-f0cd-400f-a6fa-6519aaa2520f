'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListCityProducts } from '../api/city-product-queries';
import { CityProduct } from '../types/city-product';
import { AddCityProductsModal } from './add-city-products-modal';
import { CityProductFilters } from './city-product-filters';
import { CityProductTable } from './city-product-table';
import { CityProductToggleModal } from './city-product-toggle-modal';
import { RemoveCityProductModal } from './remove-city-product-modal';

interface CityProductsTabProps {
   cityId: string;
}

export function CityProductsTab({ cityId }: CityProductsTabProps) {
   const [page, setPage] = useState(1);
   const [limit] = useState(100);
   const [search, setSearch] = useState('');
   const [vehicleTypeId, setVehicleTypeId] = useState<string | undefined>(undefined);
   const [isEnabled, setIsEnabled] = useState<string | undefined>(undefined);

   // Modal states
   const [removeModalOpen, setRemoveModalOpen] = useState(false);
   const [toggleModalOpen, setToggleModalOpen] = useState(false);
   const [selectedCityProduct, setSelectedCityProduct] = useState<CityProduct | null>(null);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleVehicleTypeChange = (value: string | undefined) => {
      setVehicleTypeId(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setIsEnabled(value);
      setPage(1);
   };

   const cityProductsQuery = useListCityProducts(cityId, {
      page,
      limit,
      search: search || undefined,
      vehicleTypeId: vehicleTypeId || undefined,
   });

   // Apply status filter client-side since the API doesn't have isEnabled filter
   const filteredData = cityProductsQuery.data
      ? {
           ...cityProductsQuery.data,
           data: cityProductsQuery.data.data.filter(product => {
              if (isEnabled === undefined) return true;
              return product.isEnabled.toString() === isEnabled;
           }),
        }
      : undefined;

   // Calculate total after filtering
   const totalCityProducts = filteredData?.data?.length || 0;

   // Handle modal actions
   const handleRemoveClick = (cityProduct: CityProduct) => {
      setSelectedCityProduct(cityProduct);
      setRemoveModalOpen(true);
   };

   const handleToggleClick = (cityProduct: CityProduct) => {
      setSelectedCityProduct(cityProduct);
      setToggleModalOpen(true);
   };

   // Check if any filters are active
   const hasFilters = !!search || !!vehicleTypeId || !!isEnabled;

   return (
      <div className='space-y-4'>
         {/* Header with Add Button */}
         <div className='flex justify-between items-center'>
            <div>
               <h3 className='text-lg font-semibold text-gray-900'>City Products</h3>
               <p className='text-sm text-gray-600'>Manage products available in this city</p>
            </div>
            <div className='flex items-center gap-4'>
               {/* Product Info */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Products</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalCityProducts}
                     </span>
                  </div>
               </div>
               <AddCityProductsModal cityId={cityId} />
            </div>
         </div>

         {/* Table Card */}
         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <CityProductFilters
               search={search}
               vehicleTypeId={vehicleTypeId}
               isEnabled={isEnabled}
               onSearchChange={handleSearchChange}
               onVehicleTypeChange={handleVehicleTypeChange}
               onStatusChange={handleStatusChange}
               isLoading={cityProductsQuery.isFetching && !cityProductsQuery.isLoading}
            />

            <CityProductTable
               data={filteredData}
               isLoading={cityProductsQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={hasFilters}
               onRemoveClick={handleRemoveClick}
               onToggleClick={handleToggleClick}
            />
         </Card>

         {/* Modals */}
         <RemoveCityProductModal
            open={removeModalOpen}
            onOpenChange={setRemoveModalOpen}
            cityProduct={selectedCityProduct}
            cityId={cityId}
         />

         <CityProductToggleModal
            open={toggleModalOpen}
            onOpenChange={setToggleModalOpen}
            cityProduct={selectedCityProduct}
         />
      </div>
   );
}
