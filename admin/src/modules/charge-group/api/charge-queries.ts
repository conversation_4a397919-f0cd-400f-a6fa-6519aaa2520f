import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { ChargeResponse, ListChargesResponse } from '../types/charge';

/**
 * Hook for listing all charges in a charge group
 */
export const useListCharges = (chargeGroupId: string | null) => {
  return useQuery({
    queryKey: ['charges', chargeGroupId],
    queryFn: (): Promise<ListChargesResponse> => {
      return apiClient.get(`/charge-groups/${chargeGroupId}/charges`);
    },
    enabled: !!chargeGroupId,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting a single charge by ID
 */
export const useGetCharge = (chargeGroupId: string | null, chargeId: string | null) => {
  return useQuery({
    queryKey: ['charge', chargeGroupId, chargeId],
    queryFn: (): Promise<ChargeResponse> => {
      return apiClient.get(`/charge-groups/${chargeGroupId}/charges/${chargeId}`);
    },
    enabled: !!chargeGroupId && !!chargeId,
    refetchOnWindowFocus: false,
  });
};