import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  ChargeGroupResponse,
  ListChargeGroupParams,
  ListChargeGroupResponse,
} from '../types/charge-group';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

/**
 * Hook for listing charge groups with pagination and filters
 */
export const useListChargeGroup = ({
  page,
  limit,
  search,
  sortBy,
  sortOrder,
}: ListChargeGroupParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.CHARGE_GROUPS.LIST),
    queryKey: ['charge-groups', page, limit, search, sortBy, sortOrder],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListChargeGroupResponse> => {
      return apiClient.get('/charge-groups/paginate', {
        params: {
          page,
          limit,
          search,
          sortBy,
          sortOrder,
        },
      });
    },
  });
};

/**
 * Hook for getting a single charge group by ID
 */
export const useGetChargeGroup = (id: string | null) => {
  return useQuery({
    queryKey: ['charge-group', id],
    queryFn: (): Promise<ChargeGroupResponse> => {
      return apiClient.get(`/charge-groups/${id || ''}`);
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};