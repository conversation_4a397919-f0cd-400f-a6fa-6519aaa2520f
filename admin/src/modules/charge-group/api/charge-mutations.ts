import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateChargeRequest, ChargeResponse, UpdateChargeRequest } from '../types/charge';

/**
 * Hook for creating a new charge
 */
export const useCreateCharge = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeGroupId: string;
      charge: CreateChargeRequest;
    }): Promise<ChargeResponse> => {
      return apiClient.post(`/charge-groups/${data.chargeGroupId}/charges`, data.charge);
    },
  });
};

/**
 * Hook for updating a charge
 */
export const useUpdateCharge = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeId: string;
      charge: UpdateChargeRequest;
    }): Promise<ChargeResponse> => {
      return apiClient.put(`/charges/${data.chargeId}`, data.charge);
    },
  });
};

/**
 * Hook for deleting a charge
 */
export const useDeleteCharge = () => {
  return useMutation({
    mutationFn: async (chargeId: string): Promise<{ success: boolean; message: string }> => {
      return apiClient.delete(`/charges/${chargeId}`);
    },
  });
};