import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsUUID,
  IsNumber,
  ArrayNotEmpty,
  ValidateNested,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

class ChargeGroupPriorityDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Charge group ID to attach to the fare rule',
  })
  @IsUUID('4', { message: 'Charge group ID must be a valid UUID' })
  chargeGroupId!: string;

  @ApiProperty({
    example: 1,
    description:
      'Priority of the charge group within the fare rule (lower values = higher priority)',
    minimum: 1,
  })
  @IsNumber({}, { message: 'Priority must be a number' })
  @Min(1, { message: 'Priority must be at least 1' })
  priority!: number;
}

export class AttachChargeGroupsToFareDto {
  @ApiProperty({
    example: [
      { chargeGroupId: '550e8400-e29b-41d4-a716-************', priority: 1 },
      { chargeGroupId: '550e8400-e29b-41d4-a716-************', priority: 2 },
    ],
    description:
      'Array of charge group IDs with their priorities to attach to the fare rule',
    type: [ChargeGroupPriorityDto],
  })
  @IsArray({ message: 'Charge groups must be an array' })
  @ArrayNotEmpty({ message: 'At least one charge group must be provided' })
  @ValidateNested({ each: true })
  @Type(() => ChargeGroupPriorityDto)
  chargeGroups!: ChargeGroupPriorityDto[];
}
