import {
  Controller,
  Get,
  Body,
  Param,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ChargeService } from '@shared/shared/modules/charge/charge.service';
import { UpdateChargeDto } from './dto/update-charge.dto';
import { ChargeResponseDto } from './dto/charge-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Charges')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('charges')
export class ChargeController {
  constructor(private readonly chargeService: ChargeService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all charges',
    description: 'Retrieve all charges across all charge groups',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charges retrieved successfully',
    type: ApiResponseDto<ChargeResponseDto[]>,
  })
  async findAll(): Promise<ApiResponseDto<ChargeResponseDto[]>> {
    const charges = await this.chargeService.findAllCharges();
    return {
      success: true,
      message: 'Charges retrieved successfully',
      data: charges as ChargeResponseDto[],
      timestamp: Date.now(),
    };
  }

  @Get('charge-group/:chargeGroupId/:chargeId')
  @ApiOperation({
    summary: 'Get charge by ID within charge group',
    description: 'Retrieve a specific charge by its ID within a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge retrieved successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or charge group not found',
    type: ApiErrorResponseDto,
  })
  async findOne(
    @Param('chargeId') chargeId: string,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.findChargeById(chargeId);
    return {
      success: true,
      message: 'Charge retrieved successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Put(':chargeId')
  @ApiOperation({
    summary: 'Update charge',
    description: 'Update a specific charge within a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge updated successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or charge group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Charge with same name already exists',
    type: ApiErrorResponseDto,
  })
  async update(
    @Param('chargeId') chargeId: string,
    @Body() updateChargeDto: UpdateChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.updateChargeById(
      chargeId,
      updateChargeDto,
    );
    return {
      success: true,
      message: 'Charge updated successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Delete(':chargeId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete charge',
    description: 'Soft delete a specific charge within a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Charge deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or charge group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge is referenced by other charges',
    type: ApiErrorResponseDto,
  })
  async remove(
    @Param('chargeGroupId') chargeGroupId: string,
    @Param('chargeId') chargeId: string,
  ): Promise<void> {
    await this.chargeService.deleteChargeByIdAndChargeGroupId(
      chargeId,
      chargeGroupId,
    );
  }

  @Put(':chargeId')
  @ApiOperation({
    summary: 'Update charge by ID only',
    description: 'Update a charge by its ID (across all charge groups)',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge updated successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  async updateById(
    @Param('chargeId') chargeId: string,
    @Body() updateChargeDto: UpdateChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.updateChargeById(
      chargeId,
      updateChargeDto,
    );
    return {
      success: true,
      message: 'Charge updated successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Delete(':chargeId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete charge by ID only',
    description: 'Delete a charge by its ID (across all charge groups)',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Charge deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge is referenced by other charges',
    type: ApiErrorResponseDto,
  })
  async removeById(@Param('chargeId') chargeId: string): Promise<void> {
    await this.chargeService.deleteChargeById(chargeId);
  }
}
