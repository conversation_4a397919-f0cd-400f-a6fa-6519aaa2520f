import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { MainOrchestratorService } from '@shared/shared/modules/ride-matching/services/main-orchestrator.service';
import { RabbitMQRideRequestMessage } from './interfaces';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Event } from '@shared/shared/common/constants/constants';
import { RideStatusUpdatedDto } from '@shared/shared/common/events/ride-status.update.event';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

@Injectable()
export class RideMatchingConsumer {
  private readonly logger = new Logger(RideMatchingConsumer.name);

  constructor(
    private readonly mainOrchestrator: MainOrchestratorService,
    private readonly eventEmitter: EventEmitter2,
    @InjectQueue('ride-processing') private readonly rideProcessingQueue: Queue,
  ) {}

  /**
   * Main ride request handler
   * Processes ride requests from RabbitMQ and orchestrates the complete matching flow
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'ride.requested',
    queue: 'ride.matching.queue',
    queueOptions: {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'ride.events.dlx',
        'x-dead-letter-routing-key': 'ride.matching.failed',
        'x-message-ttl': 300000,
        'x-max-retries': 3,
      },
    },
    errorHandler: (channel, msg, error) => {
      console.error('Error processing ride request:', error);
      channel.nack(msg, false, false);
    },
  })
  async handleRideRequest(message: RabbitMQRideRequestMessage): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `RIDE_MATCHING_CONSUMER: Received ride request for ride ${data.rideId} from RabbitMQ ` +
        `(correlationId: ${metadata.correlationId}) - Queuing for async processing`,
    );

    try {
      const event: RideStatusUpdatedDto = {
        rideId: data.rideId,
        status: RideStatus.REQUESTED,
        message: 'Your Ride is requested',
        riderId: data.riderId,
        timestamp: new Date().toISOString(),
        metadata: {
          correlationId: metadata.correlationId,
        },
      };
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);

      const jobData: any = {
        rideData: data,
        correlationId: metadata.correlationId,
      };

      await this.rideProcessingQueue.add('process-ride-initial', jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      });

      this.logger.log(
        `RIDE_MATCHING_CONSUMER: Successfully queued ride ${data.rideId} for async processing`,
      );
    } catch (error) {
      this.logger.error(
        `RIDE_MATCHING_CONSUMER: Failed to queue ride request for ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle driver acceptance messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'driver.ride.accepted',
    queue: 'ride.acceptance.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleDriverAcceptance(message: {
    eventType: string;
    timestamp: string;
    data: {
      riderId: string;
      rideId: string;
      driverId: string;
      offerId: string;
      acceptedAt: string;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;
    const startTime = Date.now();

    this.logger.log(
      `Received driver acceptance: driver ${data.driverId} accepted ride ${data.rideId} ` +
        `(correlationId: ${metadata.correlationId})`,
    );

    try {
      // Forward the acceptance to the batch orchestrator for processing
      const acceptanceHandled =
        await this.mainOrchestrator.handleDriverAcceptance(
          data.rideId,
          data.driverId,
          data.offerId,
          data.riderId,
        );

      if (acceptanceHandled) {
        // Emit driver accepted event

        this.logger.log(
          `Successfully processed driver acceptance for ride ${data.rideId}, driver ${data.driverId}`,
        );
      } else {
        this.logger.warn(
          `Failed to process driver acceptance for ride ${data.rideId}, driver ${data.driverId}`,
        );
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `Failed to handle driver acceptance for ride ${data.rideId} after ${processingTime}ms:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle ride cancellation messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'ride.cancelled',
    queue: 'ride.cancellation.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleRideCancellation(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId?: string;
      cancelledBy: 'rider' | 'driver' | 'system';
      reason: string;
      cancelledAt: string;
      isRematch?: boolean;
      originalDriverId?: string;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;
    const startTime = Date.now();

    this.logger.log(
      `Received ride cancellation: ride ${data.rideId} cancelled by ${data.cancelledBy} ` +
        `(correlationId: ${metadata.correlationId})`,
    );

    try {
      await this.mainOrchestrator.handleRideCancellation(
        data.rideId,
        data.cancelledBy,
        data.reason,
        data.driverId,
      );
      this.logger.log(
        `Successfully processed ride cancellation for ride ${data.rideId} with immediate batch interruption`,
      );
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `Failed to handle ride cancellation for ride ${data.rideId} after ${processingTime}ms:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle driver enroute messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'driver.enroute',
    queue: 'driver.enroute.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleDriverEnroute(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId: string;
      estimatedArrival: string;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `Driver ${data.driverId} is enroute to ride ${data.rideId}`,
    );

    const event: RideStatusUpdatedDto = {
      rideId: data.rideId,
      status: RideStatus.DRIVER_ENROUTE,
      message: 'Driver is on the way to your location',
      riderId: data.riderId,
      timestamp: new Date().toISOString(),
      metadata: {
        correlationId: metadata.correlationId,
      },
    };
    this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);
  }

  /**
   * Handle driver arrived messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'driver.arrived',
    queue: 'driver.arrived.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleDriverArrived(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId: string;
      arrivedAt: string;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `Driver ${data.driverId} has arrived for ride ${data.rideId}`,
    );

    const event: RideStatusUpdatedDto = {
      rideId: data.rideId,
      status: RideStatus.DRIVER_ARRIVED,
      message: 'Driver has arrived at pickup location',
      riderId: data.riderId,
      timestamp: new Date().toISOString(),
      metadata: {
        correlationId: metadata.correlationId,
      },
    };
    this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);
  }

  /**
   * Handle trip started messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'ride.started',
    queue: 'ride.started.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleTripStarted(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId: string;
      startedAt: string;
      driver: any;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `Trip started for ride ${data.rideId} with driver ${data.driverId}`,
    );

    const event: RideStatusUpdatedDto = {
      rideId: data.rideId,
      status: RideStatus.TRIP_STARTED,
      message: 'Trip has started',
      riderId: data.riderId,
      timestamp: new Date().toISOString(),
      driver: data.driver,
      metadata: {
        correlationId: metadata.correlationId,
      },
    };
    this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);

    this.eventEmitter.emit('ride.started', {
      rideId: data.rideId,
      riderId: data.riderId,
      status: 'trip_started',
      driver: data.driver,
    });
  }

  /**
   * Handle trip completed messages
   */
  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'ride.completed',
    queue: 'ride.completed.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleTripCompleted(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId: string;
      completedAt: string;
      fare?: number;
      driver: any;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `Trip completed for ride ${data.rideId} with driver ${data.driverId}`,
    );

    const event: RideStatusUpdatedDto = {
      rideId: data.rideId,
      riderId: data.riderId,
      status: RideStatus.TRIP_COMPLETED,
      message: 'Trip completed successfully',
      timestamp: new Date().toISOString(),
      driver: data.driver,
      metadata: {
        correlationId: metadata.correlationId,
      },
    };
    this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);

    this.eventEmitter.emit('ride.completed', {
      rideId: data.rideId,
      riderId: data.riderId,
      status: 'trip_completed',
      driver: data.driver,
    });
  }

  @RabbitSubscribe({
    exchange: 'ride.events',
    routingKey: 'ride.destination.reached',
    queue: 'ride.destination.reached.queue',
    queueOptions: {
      durable: true,
    },
  })
  async handleDestinationReached(message: {
    eventType: string;
    timestamp: string;
    data: {
      rideId: string;
      riderId: string;
      driverId: string;
      stopAddress: string;
      reachedAt: string;
    };
    metadata: {
      source: string;
      version: string;
      correlationId: string;
    };
  }): Promise<void> {
    const { data, metadata } = message;

    this.logger.log(
      `Destination reached for ride ${data.rideId} at stop ${data.stopAddress}`,
    );

    const event: RideStatusUpdatedDto = {
      rideId: data.rideId,
      riderId: data.riderId,
      status: RideStatus.DESTINATION_REACHED,
      message: 'Destination reached',
      timestamp: new Date().toISOString(),
      metadata: {
        correlationId: metadata.correlationId,
      },
    };
    this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);

    this.eventEmitter.emit('ride.destination.reached', {
      rideId: data.rideId,
      riderId: data.riderId,
      status: 'destination_reached',
      event: message.data,
    });
  }
}
