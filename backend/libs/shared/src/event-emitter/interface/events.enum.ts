export enum IEventName {
  // Driver events
  DRIVER_CREATED = 'driver.created',
  DRIVER_UPDATED = 'driver.updated',
  DRIVER_DELETED = 'driver.deleted',
  DRIVER_VEHICLE_CREATED = 'driverVehicle.created',
  DRIVER_VEHICLE_UPDATED = 'driverVehicle.updated',
  DRIVER_LOCATION_RECEIVED = 'driver.location.received',

  // Ride events
  RIDE_REQUESTED = 'ride.requested',
  RIDE_MATCHED = 'ride.matched',
  RIDE_CANCELLED = 'ride.cancelled',
  RIDE_COMPLETED = 'ride.completed',
  RIDE_STARTED = 'ride.started',
  RIDE_ARRIVED = 'ride.arrived',
  RIDE_DESTINATION_REACHED = 'ride.destination.reached',
  RIDE_STATUS_UPDATED = 'ride.status.updated',

  // WebSocket notification events (separate from ride events to prevent loops)
  WEBSOCKET_RIDE_STATUS_UPDATE = 'websocket.ride.status.update',
  WEBSOCKET_DRIVER_OFFER = 'websocket.driver.offer',
  WEBSOCKET_DRIVER_OFFER_TIMEOUT = 'websocket.driver.offer.timeout',
  WEBSOCKET_DRIVER_LOCATION_UPDATE = 'websocket.driver.location.update',
}
