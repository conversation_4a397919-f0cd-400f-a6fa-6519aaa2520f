import { IEventName } from '../interface/events.enum';

export interface EventRouting {
  exchange: string;
  routingKey: string;
  queue?: string;
}

/**
 * Centralized event routing configuration
 * Maps event names to their RabbitMQ routing configuration
 */
export const EVENT_ROUTING_MAP: Record<IEventName, EventRouting> = {
  // Driver Events
  [IEventName.DRIVER_CREATED]: {
    exchange: 'driver.events',
    routingKey: 'driver.created',
    queue: 'driver.audit.queue',
  },
  [IEventName.DRIVER_UPDATED]: {
    exchange: 'driver.events',
    routingKey: 'driver.updated',
    queue: 'driver.audit.queue',
  },
  [IEventName.DRIVER_DELETED]: {
    exchange: 'driver.events',
    routingKey: 'driver.deleted',
    queue: 'driver.audit.queue',
  },
  [IEventName.DRIVER_VEHICLE_CREATED]: {
    exchange: 'driver.events',
    routingKey: 'driver.vehicle.created',
    queue: 'driver.audit.queue',
  },
  [IEventName.DRIVER_VEHICLE_UPDATED]: {
    exchange: 'driver.events',
    routingKey: 'driver.vehicle.updated',
    queue: 'driver.audit.queue',
  },
  [IEventName.DRIVER_LOCATION_RECEIVED]: {
    exchange: 'driver.events',
    routingKey: 'driver.location.received',
    queue: 'location.processing.queue',
  },

  // Ride Events
  [IEventName.RIDE_REQUESTED]: {
    exchange: 'ride.events',
    routingKey: 'ride.requested',
    queue: 'ride.matching.queue.priority',
  },
  [IEventName.RIDE_MATCHED]: {
    exchange: 'ride.events',
    routingKey: 'ride.matched',
    queue: 'ride.notifications.queue',
  },
  [IEventName.RIDE_CANCELLED]: {
    exchange: 'ride.events',
    routingKey: 'ride.cancelled',
    queue: 'ride.matching.cancellation.queue',
  },
  [IEventName.RIDE_COMPLETED]: {
    exchange: 'ride.events',
    routingKey: 'ride.completed',
    queue: 'ride.billing.queue',
  },
  [IEventName.RIDE_STARTED]: {
    exchange: 'ride.events',
    routingKey: 'ride.started',
    queue: 'ride.tracking.queue',
  },
  [IEventName.RIDE_ARRIVED]: {
    exchange: 'ride.events',
    routingKey: 'ride.arrived',
    queue: 'ride.notifications.queue',
  },
  [IEventName.RIDE_DESTINATION_REACHED]: {
    exchange: 'ride.events',
    routingKey: 'ride.destination.reached',
    queue: 'ride.notifications.queue',
  },
  [IEventName.RIDE_STATUS_UPDATED]: {
    exchange: 'ride.events',
    routingKey: 'ride.status.updated',
    queue: 'ride.notifications.queue',
  },

  [IEventName.WEBSOCKET_RIDE_STATUS_UPDATE]: {
    exchange: 'websocket.notifications',
    routingKey: 'websocket.ride.status.update',
    queue: 'websocket.notifications.queue',
  },
  [IEventName.WEBSOCKET_DRIVER_OFFER]: {
    exchange: 'websocket.notifications',
    routingKey: 'websocket.driver.offer',
    queue: 'websocket.notifications.queue',
  },
  [IEventName.WEBSOCKET_DRIVER_OFFER_TIMEOUT]: {
    exchange: 'websocket.notifications',
    routingKey: 'websocket.driver.offer.timeout',
    queue: 'websocket.notifications.queue',
  },
  [IEventName.WEBSOCKET_DRIVER_LOCATION_UPDATE]: {
    exchange: 'websocket.notifications',
    routingKey: 'websocket.driver.location.update',
    queue: 'websocket.notifications.queue',
  },
};

/**
 * Get routing configuration for an event
 */
export function getEventRouting(eventName: IEventName): EventRouting {
  const routing = EVENT_ROUTING_MAP[eventName];
  if (!routing) {
    throw new Error(`No routing configuration found for event: ${eventName}`);
  }
  return routing;
}

/**
 * Get all events for a specific exchange
 */
export function getEventsForExchange(exchange: string): IEventName[] {
  return Object.keys(EVENT_ROUTING_MAP).filter(
    (eventName) =>
      EVENT_ROUTING_MAP[eventName as IEventName].exchange === exchange,
  ) as IEventName[];
}

/**
 * Get all unique exchanges
 */
export function getAllExchanges(): string[] {
  return [
    ...new Set(
      Object.values(EVENT_ROUTING_MAP).map((routing) => routing.exchange),
    ),
  ];
}

/**
 * Get all unique queues
 */
export function getAllQueues(): string[] {
  return [
    ...new Set(
      Object.values(EVENT_ROUTING_MAP)
        .map((routing) => routing.queue)
        .filter((queue) => queue !== undefined),
    ),
  ] as string[];
}
