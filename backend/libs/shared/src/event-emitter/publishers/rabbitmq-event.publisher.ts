import { Injectable, Logger } from '@nestjs/common';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { IEventName } from '../interface/events.enum';
import { getEventRouting } from '../constants/event-routing.constants';
import { RideData } from '@shared/shared/modules/ride-matching/interfaces';

@Injectable()
export class RabbitMQEventPublisher {
  private readonly logger = new Logger(RabbitMQEventPublisher.name);
  private publishedToday = 0;
  private failedToday = 0;

  constructor(private readonly amqpConnection: AmqpConnection) {}

  /**
   * Publish ride requested event to RabbitMQ
   */
  async publishRideRequested(data: RideData): Promise<void> {
    try {
      const routing = getEventRouting(IEventName.RIDE_REQUESTED);
      const correlationId = `ride_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing to exchange: "${routing.exchange}", routingKey: "${routing.routingKey}", queue: "${routing.queue}"`,
      );

      const eventData = {
        eventType: IEventName.RIDE_REQUESTED,
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'api',
          version: '1.0.0',
          correlationId,
          priority: data.mode === 'now' ? 'high' : 'normal',
        },
      };
      const publishOptions =
        data.mode === 'now' ? { priority: 10 } : { priority: 1 };

      await this.amqpConnection.publish(
        routing.exchange,
        routing.routingKey,
        eventData,
        publishOptions,
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published ride requested event for ride ${data.rideId} (mode: ${data.mode})`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish ride requested event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  async publishRideProcessingStarted(data: {
    rideId: string;
    riderId: string;
  }): Promise<void> {
    const routing = getEventRouting(IEventName.WEBSOCKET_RIDE_STATUS_UPDATE);

    await this.amqpConnection.publish(
      routing.exchange,
      routing.routingKey,
      data,
    );
  }

  /**
   * Publish WebSocket events to RabbitMQ
   */
  async publishWebSocketEvent(routingKey: string, data: any): Promise<void> {
    try {
      const eventData = {
        ...data,
        timestamp: data.timestamp || new Date().toISOString(),
        correlationId:
          data.correlationId ||
          `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      const publishOptions = {
        persistent: true,
        timestamp: Date.now(),
        headers: {
          eventType: routingKey,
          source: 'notifier-service',
          version: '1.0.0',
        },
      };

      await this.amqpConnection.publish(
        'websocket.notifications',
        routingKey,
        eventData,
        publishOptions,
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published WebSocket event ${routingKey}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish WebSocket event ${routingKey}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Publish ride matched event to RabbitMQ
   * This event is consumed by the RideMatchingConsumer.handleDriverAcceptance method
   */
  async publishRideMatched(data: {
    rideId: string;
    riderId: string;
    driverId: string;
    offerId: string;
    acceptedAt: string;
  }): Promise<void> {
    try {
      const correlationId = `ride_matched_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing ride matched event to exchange: "ride.events", routingKey: "driver.ride.accepted"`,
      );

      const eventData = {
        eventType: 'driver.ride.accepted',
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'api',
          version: '1.0.0',
          correlationId,
        },
      };

      await this.amqpConnection.publish(
        'ride.events',
        'driver.ride.accepted',
        eventData,
        {
          persistent: true,
          timestamp: Date.now(),
        },
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published ride matched event for ride ${data.rideId}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish ride matched event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Publish ride cancelled event
   */
  async publishRideCancelled(data: {
    rideId: string;
    riderId: string;
    driverId?: string;
    cancelledBy: 'rider' | 'driver' | 'system';
    reason: string;
    cancelledAt: string;
    isRematch?: boolean;
    originalDriverId?: string;
  }): Promise<void> {
    try {
      const correlationId = `ride_cancelled_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing ride cancelled event to exchange: "ride.events", routingKey: "ride.cancelled"`,
      );

      const eventData = {
        eventType: 'ride.cancelled',
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'ride-service',
          version: '1.0.0',
          correlationId,
        },
      };

      await this.amqpConnection.publish(
        'ride.events',
        'ride.cancelled',
        eventData,
        {
          persistent: true,
          timestamp: Date.now(),
        },
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published ride cancelled event for ride ${data.rideId}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish ride cancelled event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  async publishRideCompleted(data: {
    rideId: string;
    riderId: string;
    driverId: string;
    completedAt: string;
    actualFare: number;
    distance?: number;
    duration?: number;
    driver: any;
    rating?: {
      riderRating?: number;
      driverRating?: number;
    };
  }): Promise<void> {
    try {
      const correlationId = `ride_completed_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing ride completed event to exchange: "ride.events", routingKey: "ride.completed"`,
      );

      const eventData = {
        eventType: 'ride.completed',
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'ride-service',
          version: '1.0.0',
          correlationId,
        },
      };

      await this.amqpConnection.publish(
        'ride.events',
        'ride.completed',
        eventData,
        {
          persistent: true,
          timestamp: Date.now(),
        },
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published ride completed event for ride ${data.rideId}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish ride completed event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  async publishRideStarted(data: {
    rideId: string;
    riderId: string;
    driverId: string;
    startedAt: string;
    driver: any;
    pickupLocation: { latitude: number; longitude: number };
  }): Promise<void> {
    try {
      const correlationId = `ride_started_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing ride started event to exchange: "ride.events", routingKey: "ride.started"`,
      );

      const eventData = {
        eventType: 'ride.started',
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'ride-service',
          version: '1.0.0',
          correlationId,
        },
      };

      await this.amqpConnection.publish(
        'ride.events',
        'ride.started',
        eventData,
        {
          persistent: true,
          timestamp: Date.now(),
        },
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published ride started event for ride ${data.rideId}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish ride started event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  async publishDestinationReached(data: {
    rideId: string;
    riderId: string;
    driverId: string;
    stopAddress: string;
    reachedAt: string;
  }): Promise<void> {
    try {
      const correlationId = `destination_reached_${data.rideId}_${Date.now()}`;

      this.logger.log(
        `RABBITMQ_PUBLISHER: Publishing destination reached event to exchange: "ride.events", routingKey: "ride.destination.reached"`,
      );

      const eventData = {
        eventType: 'ride.destination.reached',
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          source: 'ride-service',
          version: '1.0.0',
          correlationId,
        },
      };

      await this.amqpConnection.publish(
        'ride.events',
        'ride.destination.reached',
        eventData,
        {
          persistent: true,
          timestamp: Date.now(),
        },
      );

      this.publishedToday++;
      this.logger.log(
        `RABBITMQ_PUBLISHER: Successfully published destination reached event for ride ${data.rideId}`,
      );
    } catch (error) {
      this.failedToday++;
      this.logger.error(
        `RABBITMQ_PUBLISHER: Failed to publish destination reached event for ride ${data.rideId}:`,
        error,
      );
      throw error;
    }
  }

  // async publishRideStatusUpdated(data: {
  //   rideId: string;
  //   riderId: string;
  //   driverId: string;
  //   status: string;
  //   updatedAt: string;
  // }): Promise<void> {
  //   try {
  //     const correlationId = `ride_status_updated_${data.rideId}_${Date.now()}`;

  //     this.logger.log(
  //       `RABBITMQ_PUBLISHER: Publishing ride status updated event to exchange: "ride.events", routingKey: "ride.status.updated"`,
  //     );

  //     const eventData = {
  //       eventType: 'ride.status.updated',
  //       timestamp: new Date().toISOString(),
  //       data,
  //       metadata: {
  //         source: 'ride-service',
  //         version: '1.0.0',
  //         correlationId,
  //       },
  //     };

  //     await this.amqpConnection.publish(
  //       'ride.events',
  //       'ride.status.updated',
  //       eventData,
  //       {
  //         persistent: true,
  //         timestamp: Date.now(),
  //       },
  //     );

  //     this.publishedToday++;
  //     this.logger.log(
  //       `RABBITMQ_PUBLISHER: Successfully published ride status updated event for ride ${data.rideId}`,
  //     );
  //   } catch (error) {
  //     this.failedToday++;
  //     this.logger.error(
  //       `RABBITMQ_PUBLISHER: Failed to publish ride status updated event for ride ${data.rideId}:`,
  //       error,
  //     );
  //     throw error;
  //   }
  // }
}
