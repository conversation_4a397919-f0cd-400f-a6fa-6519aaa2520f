/**
 * Centralized constants for ride-matching service
 * All hardcoded values should be defined here to ensure consistency
 */

// =============================================================================
// GENERAL CONFIGURATION
// =============================================================================

export const RIDE_MATCHING_CONFIG = {
  DEFAULT_SEARCH_RADIUS: 2, // H3 rings
  DEFAULT_MAX_DRIVERS: 50,
  DEFAULT_TIMEOUT_MS: 30000, // 30 seconds
  DEFAULT_H3_RESOLUTION: 8, // H3 resolution for driver matching
  EARTH_RADIUS_KM: 6371, // Earth's radius in kilometers
} as const;

// =============================================================================
// BATCHING CONFIGURATION
// =============================================================================

export const BATCHING_CONFIG = {
  DEFAULT_ACCEPT_WINDOW_MS: 10000, // 10 seconds
  DEFAULT_MAX_BATCHES: 10,
  DEFAULT_TOTAL_TIMEOUT_MS: 300000, // 5 minutes
  DEFAULT_BATCH_SIZE_PERCENTAGE: 0.15, // 15%
  DEFAULT_MAX_BATCH_SIZE: 5,
} as const;

// =============================================================================
// PENALTY CONFIGURATION
// =============================================================================

export const PENALTY_CONFIG = {
  DEFAULT_BATCH_NON_RESPONSE_PENALTY: 15, // points
  DEFAULT_PENALTY_DECAY_RATE: 0.7, // 30% reduction per batch
  DEFAULT_PENALTY_TTL: 1800, // 30 minutes in seconds
  DEFAULT_RECENT_REJECTION_PENALTY: 2, // points per rejection
  DEFAULT_MAX_RECENT_REJECTION_PENALTY: 10, // points
  DEFAULT_REJECTION_TIME_WINDOW: 3600, // 1 hour in seconds

  // Redis key prefixes
  PENALTY_PREFIX: 'driver_penalty:',
  PENALTY_HISTORY_PREFIX: 'driver_penalty_history:',
  REJECTION_HISTORY_PREFIX: 'driver_rejection_history:',

  // History limits
  MAX_PENALTY_HISTORY: 10,
  MAX_REJECTION_HISTORY: 20,
  PENALTY_HISTORY_TTL: 86400, // 24 hours in seconds
} as const;

// =============================================================================
// SCORING CONFIGURATION
// =============================================================================

export const SCORING_CONFIG = {
  // ETA scoring (40 points max)
  ETA: {
    MIN: 2, // 2 minutes
    MAX: 8, // 8 minutes
    WEIGHT: 40,
  },

  // Idle time scoring (20 points max)
  IDLE_TIME: {
    MIN: 0, // 0 minutes
    MAX: 30, // 30 minutes
    WEIGHT: 20,
  },

  // Accept rate scoring (15 points max)
  ACCEPT_RATE: {
    MIN: 0.7, // 70%
    MAX: 0.98, // 98%
    WEIGHT: 15,
  },

  // Rating scoring (12 points max)
  RATING: {
    MIN: 4.5, // 4.5 stars
    MAX: 5.0, // 5.0 stars
    WEIGHT: 12,
  },

  // Ending nearby scoring (12 points max)
  ENDING_NEARBY: {
    MIN: 0.2, // 0.2 km
    MAX: 2.0, // 2.0 km
    WEIGHT: 12,
  },

  // Zone transition bonus (8 points max)
  ZONE_TRANSITION: {
    BONUS: 10,
    WEIGHT: 0.8,
  },

  // Going home alignment bonus (10 points max)
  GOING_HOME_ALIGN: {
    BONUS: 10,
    WEIGHT: 1.0,
  },

  // New driver bonus (6 points max)
  NEW_DRIVER_BONUS: {
    BONUS: 10,
    WEIGHT: 0.6,
    THRESHOLD: 100, // rides
  },

  // Score distribution ranges for analytics
  SCORE_RANGES: [
    { min: 0, max: 25, label: '0-25' },
    { min: 25, max: 50, label: '25-50' },
    { min: 50, max: 75, label: '50-75' },
    { min: 75, max: 100, label: '75-100' },
    { min: 100, max: 125, label: '100-125' },
    { min: 125, max: 150, label: '125-150' },
  ] as const,

  // Maximum possible scores
  MAX_BASE_SCORE: 135, // Sum of all individual max scores

  // Defaults for error cases
  DEFAULT_RATING: 4.5,
  DEFAULT_ACCEPT_RATE: 0.85,
  DEFAULT_LIFETIME_RIDES: 0,
  DEFAULT_HEADING: 0,
} as const;

// =============================================================================
// STATUS ENUMS
// =============================================================================

export enum DriverStatus {
  ONLINE = 'online',
  BUSY = 'busy',
  IN_RIDE = 'in_ride',
  OFFLINE = 'offline',
}

export enum RideStatus {
  REQUESTED = 'requested',
  PROCESSING = 'processing',
  MATCHED = 'matched',
  ACCEPTED = 'accepted',
  DRIVER_ENROUTE = 'driver_enroute',
  DRIVER_ARRIVED = 'driver_arrived',
  TRIP_STARTED = 'trip_started',
  DESTINATION_REACHED = 'destination_reached',
  TRIP_COMPLETED = 'trip_completed',
  CANCELLED = 'cancelled',
  SEARCHING_DRIVERS = 'searching_drivers',
  UNASSIGNED = 'unassigned',
  CANCELLED_BY_RIDER = 'cancelled_by_rider',
  CANCELLED_BY_DRIVER = 'cancelled_by_driver',
}

export enum RideOfferStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

export enum BatchStatus {
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
}

export enum CancellationSource {
  RIDER = 'rider',
  DRIVER = 'driver',
  SYSTEM = 'system',
}

export enum UserType {
  RIDER = 'rider',
  DRIVER = 'driver',
}

export enum RideMode {
  NOW = 'now',
  LATER = 'later',
}

export enum UrgencyLevel {
  IMMEDIATE = 'immediate',
  URGENT = 'urgent',
  NORMAL = 'normal',
  SCHEDULED = 'scheduled',
}

export enum ZoneAlgorithm {
  CITY = 'CITY',
  AIRPORT = 'AIRPORT',
  HIGHWAY = 'HIGHWAY',
  SUBURBAN = 'SUBURBAN',
}

export enum ScoringPhase {
  CORE = 'core',
  EXTENDED = 'extended',
}

// =============================================================================
// ALGORITHM CONFIGURATION
// =============================================================================

export const ALGORITHM_CONFIG = {
  DEFAULT_MAX_DRIVERS: 100,
  DEFAULT_TIMEOUT_MS: 30000,
  DEFAULT_SCORING_PHASE: ScoringPhase.CORE,

  // Validation limits
  MIN_DRIVERS: 1,
  MAX_DRIVERS_LIMIT: 100,
  MIN_TIMEOUT_MS: 1000,
  MAX_TIMEOUT_MS: 60000,

  // Default bearing alignment tolerance
  DEFAULT_BEARING_TOLERANCE: 45, // degrees
} as const;

// =============================================================================
// REALTIME/WEBSOCKET CONFIGURATION
// =============================================================================

export const REALTIME_CONFIG = {
  // Socket event names
  EVENTS: {
    RIDE_OFFER: 'ride_offer',
    RIDE_STATUS_UPDATE: 'ride_status_update',
    DRIVER_LOCATION_UPDATE: 'driver_location_update',
    RIDE_SUBSCRIPTION_CONFIRMED: 'ride_subscription_confirmed',
    RIDE_MATCHING_STARTED: 'ride_matching_started',
    RIDE_MATCHING_COMPLETED: 'ride_matching_completed',
    RIDE_ACCEPTED: 'ride_accepted',
    RIDE_CANCELLED: 'ride_cancelled',
    OFFER_TIMEOUT: 'offer_timeout',
    SUBSCRIBE_RIDE: 'subscribe_ride',
    UNSUBSCRIBE_RIDE: 'unsubscribe_ride',
  },

  // Status messages
  STATUS_MESSAGES: {
    [RideStatus.REQUESTED]: 'Your ride request has been received',
    [RideStatus.PROCESSING]: 'Finding nearby drivers...',
    [RideStatus.SEARCHING_DRIVERS]: 'Searching for available drivers...',
    [RideStatus.MATCHED]: 'Driver found! Ride request sent.',
    [RideStatus.ACCEPTED]: 'Driver is on the way!',
    [RideStatus.DRIVER_ENROUTE]: 'Driver is heading to your location',
    [RideStatus.DRIVER_ARRIVED]: 'Driver has arrived at pickup location',
    [RideStatus.TRIP_STARTED]: 'Trip has started',
    [RideStatus.TRIP_COMPLETED]: 'Trip completed successfully',
    [RideStatus.CANCELLED]: 'Ride has been cancelled',
  } as const,
} as const;

// =============================================================================
// CACHE/REDIS CONFIGURATION
// =============================================================================

export const CACHE_CONFIG = {
  RIDE_PROCESSING_PREFIX: 'ride_processing:',
  BATCH_STATE_PREFIX: 'batch_state:',
  MATCHING_METRICS_PREFIX: 'matching_metrics:',
  RIDE_LOCK_PREFIX: 'ride_lock:',

  // TTL values
  DEFAULT_PROCESSING_STATUS_TTL: 3600, // 1 hour
  DEFAULT_BATCH_STATE_TTL: 1800, // 30 minutes
  DEFAULT_METRICS_TTL: 7200, // 2 hours
  DEFAULT_RIDE_LOCK_TTL: 300, // 5 minutes
} as const;

// =============================================================================
// VALIDATION CONSTANTS
// =============================================================================

export const VALIDATION_LIMITS = {
  MIN_LAT: -90,
  MAX_LAT: 90,
  MIN_LNG: -180,
  MAX_LNG: 180,
  MIN_RATING: 1,
  MAX_RATING: 5,
  MIN_ACCEPT_RATE: 0,
  MAX_ACCEPT_RATE: 1,
  MIN_DISTANCE: 0,
  MAX_DISTANCE: 1000, // km
} as const;
