import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  CityProductFare,
  CityProductFareStatus,
} from './models/cityProductFare.model';

@Injectable()
export class CityProductFareRepository extends BaseRepository<CityProductFare> {
  protected readonly modelName = 'cityProductFare';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new city product fare record.
   * @param data - City product fare data excluding id and timestamps
   */
  async createCityProductFare(
    data: Omit<CityProductFare, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CityProductFare> {
    return this.create(data);
  }

  /**
   * Find all city product fares for a specific city product.
   * @param cityProductId - City product ID
   */
  async findCityProductFaresByCityProductId(
    cityProductId: string,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: { cityProductId },
      include: {
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find city product fare by ID with relations.
   * @param id - City product fare ID
   */
  async findCityProductFareById(id: string): Promise<CityProductFare | null> {
    return this.findById(id, {
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
    });
  }

  /**
   * Update city product fare by ID.
   * @param id - City product fare ID
   * @param data - Partial city product fare data
   */
  async updateCityProductFare(
    id: string,
    data: Partial<CityProductFare>,
  ): Promise<CityProductFare> {
    return this.updateById(id, data);
  }

  /**
   * Soft delete city product fare by ID.
   * @param id - City product fare ID
   */
  async deleteCityProductFare(id: string): Promise<CityProductFare> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated city product fares.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateCityProductFares(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      ...options,
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find fare rules that match pickup and destination zones.
   * @param pickupZoneId - Pickup zone ID (can be null for default rules)
   * @param destinationZoneId - Destination zone ID (can be null for default rules)
   */
  async findMatchingFareRules(
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: {
        status: CityProductFareStatus.ACTIVE,
        OR: [
          {
            fromZoneId: pickupZoneId,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: pickupZoneId,
            toZoneId: null,
          },
          {
            fromZoneId: null,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: null,
            toZoneId: null,
          },
        ],
      },
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Attach charge groups to a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupIds - Array of charge group IDs with priorities
   */
  async attachChargeGroupsToFare(
    cityProductFareId: string,
    chargeGroupIds: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    return this.prisma.$transaction(async (tx) => {
      for (const { chargeGroupId, priority } of chargeGroupIds) {
        // Try to update priority if exists, else create new association
        const updated = await tx.fareChargeGroup.updateMany({
          where: {
            cityProductFareId,
            chargeGroupId,
          },
          data: { priority },
        });
        if (updated.count === 0) {
          await tx.fareChargeGroup.create({
            data: {
              cityProductFareId,
              chargeGroupId,
              priority,
            },
          });
        }
      }

      return tx.fareChargeGroup.findMany({
        where: { cityProductFareId },
        include: {
          chargeGroup: true,
        },
        orderBy: { priority: 'asc' },
      });
    });
  }

  /**
   * Detach a charge group from a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupId - Charge group ID
   */
  async detachChargeGroupFromFare(
    cityProductFareId: string,
    chargeGroupId: string,
  ): Promise<void> {
    await this.prisma.fareChargeGroup.deleteMany({
      where: {
        cityProductFareId,
        chargeGroupId,
      },
    });
  }

  /**
   * Update charge group priorities for a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupPriorities - Array of charge group IDs with new priorities
   */
  async updateChargeGroupPriorities(
    cityProductFareId: string,
    chargeGroupPriorities: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    // Update priorities for existing charge groups using Prisma repo functions
    for (const { chargeGroupId, priority } of chargeGroupPriorities) {
      await this.prisma.fareChargeGroup.updateMany({
        where: {
          cityProductFareId,
          chargeGroupId,
        },
        data: {
          priority,
          updatedAt: new Date(),
        },
      });
    }

    // Return updated fare charge groups using Prisma
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Get charge groups for a specific fare rule.
   * @param cityProductFareId - City product fare ID
   */
  async getFareChargeGroups(cityProductFareId: string): Promise<any[]> {
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Check if a fare rule exists for a city product with specific zones.
   * @param cityProductId - City product ID
   * @param fromZoneId - From zone ID (nullable)
   * @param toZoneId - To zone ID (nullable)
   */
  async fareRuleExists(
    cityProductId: string,
    fromZoneId?: string | null,
    toZoneId?: string | null,
  ): Promise<any> {
    return this.findOne({
      where: {
        cityProductId,
        fromZoneId,
        toZoneId,
      },
    });
  }
}
