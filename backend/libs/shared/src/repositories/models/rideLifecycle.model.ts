import { BaseEntity } from '../base.repository';
import { LocationPoint, Ride } from './ride.model';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

export interface RideLifecycleMeta {
  cancellationReason?: string;
  cancelledBy?: string; // user profile id
  cancelledByRole?: string; // 'driver' | 'rider' | 'admin'
  estimatedArrivalTime?: string;
  actualArrivalTime?: string;
  notes?: string;
  [key: string]: any; // Allow additional metadata
}

export interface RideLifecycle extends BaseEntity {
  rideId: string;
  status: RideStatus;
  meta?: RideLifecycleMeta | null;
  location?: LocationPoint | null;

  // Relations
  ride?: Ride;
}
