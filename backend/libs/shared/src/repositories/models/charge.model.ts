import { BaseEntity } from '../base.repository';

export enum ChargeType {
  FLAT = 'flat',
  METERED = 'metered',
}

export enum ChargeMeter {
  PICKUP_DISTANCE = 'pickup_distance',
  PICKUP_DURATION = 'pickup_duration',
  PICKUP_WAIT_DURATION = 'pickup_wait_duration',
  TRIP_DURATION = 'trip_duration',
  TRIP_WAIT_DURATION = 'trip_wait_duration',
  TRIP_DISTANCE = 'trip_distance',
}

export enum PriceModel {
  FLAT_AMOUNT = 'flat_amount',
  LINEAR_RATE = 'linear_rate',
  TIERED = 'tiered',
  PERCENTAGE_OF_CHARGE = 'percentage_of_charge',
  FORMULA = 'formula',
}

export interface TierConfig {
  From: number;
  To: number | 'inf';
  Flat_fee?: number;
  Rate?: number;
  currency?: string;
}

export interface PriceConfig {
  amount?: number;
  currency?: string;
  rate?: number;
  tiers?: TierConfig[];
  formula?: string;
}

export interface Charge extends BaseEntity {
  name: string;
  identifier?: string;
  chargeType: ChargeType;
  condition?: any | null;
  meter?: ChargeMeter | null;
  priceModel: PriceModel;
  price: PriceConfig;
  percentage?: number | null;
  percentageOfChargeId?: string | null;
  percentageOfCharge?: Charge | null;
  percentageCharges?: Charge[];
}
