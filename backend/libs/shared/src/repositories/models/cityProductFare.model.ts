import { BaseEntity } from '../base.repository';
import { CityProduct } from './cityProduct.model';
import { FareChargeGroup } from './fareChargeGroup.model';
import { Zone } from './zone.model';

export enum CityProductFareStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface CityProductFare extends BaseEntity {
  cityProductId: string;
  priority: number;
  status: CityProductFareStatus;
  fromZoneId?: string | null;
  toZoneId?: string | null;

  // Relations
  cityProduct?: CityProduct;
  fromZone?: Zone | null;
  toZone?: Zone | null;
  fareChargeGroups?: FareChargeGroup[];
}
