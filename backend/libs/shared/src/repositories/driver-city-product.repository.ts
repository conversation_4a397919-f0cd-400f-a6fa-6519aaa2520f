import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { DriverCityProduct } from './models/driverCityProduct.model';

@Injectable()
export class DriverCityProductRepository extends BaseRepository<DriverCityProduct> {
  protected readonly modelName = 'driverCityProduct';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Add city products to a driver.
   * @param userProfileId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs
   */
  async addCityProductsToDriver(
    userProfileId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    const data = cityProductIds.map((cityProductId) => ({
      userProfileId,
      cityProductId,
    }));

    // Use createMany for bulk insert
    await this.prisma.driverCityProduct.createMany({
      data,
      skipDuplicates: true, // Skip if relationship already exists
    });

    // Return the created records with relations
    return this.findDriverCityProductsByDriverId(userProfileId);
  }

  /**
   * Remove city products from a driver.
   * @param userProfileId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs
   */
  async removeCityProductsFromDriver(
    userProfileId: string,
    cityProductIds: string[],
  ): Promise<void> {
    await this.prisma.driverCityProduct.deleteMany({
      where: {
        userProfileId,
        cityProductId: {
          in: cityProductIds,
        },
      },
    });
  }

  /**
   * Find all city products for a driver.
   * @param userProfileId - Driver's user profile ID
   */
  async findDriverCityProductsByDriverId(
    userProfileId: string,
  ): Promise<DriverCityProduct[]> {
    return this.findMany({
      where: { userProfileId },
      include: {
        cityProduct: {
          include: {
            city: {
              select: { id: true, name: true, state: true, country: true },
            },
            product: {
              select: { id: true, name: true, description: true, icon: true },
            },
            vehicleType: {
              select: { id: true, name: true, description: true },
            },
          },
        },
        userProfile: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });
  }

  /**
   * Paginate city products for a driver with search.
   * @param userProfileId - Driver's user profile ID
   * @param page - Page number
   * @param limit - Items per page
   * @param productName - Product name search term
   */
  async paginateDriverCityProducts(
    userProfileId: string,
    page = 1,
    limit = 10,
    productName?: string,
  ) {
    const whereClause: any = {
      userProfileId,
    };

    if (productName) {
      whereClause.cityProduct = {
        product: {
          name: {
            contains: productName,
            mode: 'insensitive',
          },
        },
      };
    }

    return this.paginate(page, limit, {
      where: whereClause,
      include: {
        cityProduct: {
          include: {
            city: {
              select: { id: true, name: true, state: true, country: true },
            },
            product: {
              select: { id: true, name: true, description: true, icon: true },
            },
            vehicleType: {
              select: { id: true, name: true, description: true },
            },
          },
        },
        userProfile: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Check if driver-city-product relationship exists.
   * @param userProfileId - Driver's user profile ID
   * @param cityProductId - City product ID
   */
  async driverCityProductExists(
    userProfileId: string,
    cityProductId: string,
  ): Promise<boolean> {
    const record = await this.findOne({
      where: {
        userProfileId,
        cityProductId,
      },
    });
    return !!record;
  }

  /**
   * Get driver city products by city product IDs.
   * @param userProfileId - Driver's user profile ID
   * @param cityProductIds - Array of city product IDs
   */
  async findDriverCityProductsByCityProductIds(
    userProfileId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    return this.findMany({
      where: {
        userProfileId,
        cityProductId: {
          in: cityProductIds,
        },
      },
      include: {
        cityProduct: {
          include: {
            city: {
              select: { id: true, name: true, state: true, country: true },
            },
            product: {
              select: { id: true, name: true, description: true, icon: true },
            },
            vehicleType: {
              select: { id: true, name: true, description: true },
            },
          },
        },
        userProfile: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });
  }

  /**
   * Remove all city products from a driver.
   * @param userProfileId - Driver's user profile ID
   */
  async removeAllCityProductsFromDriver(userProfileId: string): Promise<void> {
    await this.prisma.driverCityProduct.deleteMany({
      where: { userProfileId },
    });
  }

  /**
   * Get count of city products for a driver.
   * @param userProfileId - Driver's user profile ID
   */
  async getDriverCityProductsCount(userProfileId: string): Promise<number> {
    return this.prisma.driverCityProduct.count({
      where: { userProfileId },
    });
  }

  /**
   * Find drivers who can serve specific products
   * DB Query 1: Optimized query to get driver-product mappings
   * @param driverIds - Array of driver user profile IDs
   * @param productIds - Array of product IDs to match
   * @returns Array of objects with userProfileId and productId
   */

  /*
  TODO: Refactor this query to reduce the number of queries made to the database.
  */
  async findDriversForProducts(
    driverIds: string[],
    productIds: string[],
  ): Promise<{ userProfileId: string; productId: string }[]> {
    const result = await this.prisma.driverCityProduct.findMany({
      where: {
        userProfileId: {
          in: driverIds,
        },
        cityProduct: {
          isEnabled: true,
          productId: {
            in: productIds,
          },
        },
      },
      select: {
        userProfileId: true,
        cityProduct: {
          select: {
            productId: true,
          },
        },
      },
    });
    console.log('Driver-Product Mappings:', result);
    // Flatten the result to get userProfileId and productId pairs
    return result.map((item) => ({
      userProfileId: item.userProfileId,
      productId: item.cityProduct.productId,
    }));
  }

  /**
   * Add city products to a driver vehicle.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductIds - Array of city product IDs
   */
  async addCityProductsToDriverVehicle(
    userProfileId: string,
    driverVehicleId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    const data = cityProductIds.map((cityProductId) => ({
      userProfileId,
      driverVehicleId,
      cityProductId,
      isPrimary: false,
    }));

    // Use createMany for bulk insert
    await this.prisma.driverCityProduct.createMany({
      data,
      skipDuplicates: true, // Skip if relationship already exists
    });

    // Return the created records with relations
    return this.findDriverCityProductsByDriverVehicle(
      userProfileId,
      driverVehicleId,
    );
  }

  /**
   * Find city products by driver vehicle and city product IDs.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductIds - Array of city product IDs
   */
  async findDriverCityProductsByDriverVehicleAndCityProducts(
    userProfileId: string,
    driverVehicleId: string,
    cityProductIds: string[],
  ): Promise<DriverCityProduct[]> {
    return this.findMany({
      where: {
        userProfileId,
        driverVehicleId,
        cityProductId: { in: cityProductIds },
      },
    });
  }

  /**
   * Remove city products from a driver vehicle.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductIds - Array of city product IDs to remove
   */
  async removeCityProductsFromDriverVehicle(
    userProfileId: string,
    driverVehicleId: string,
    cityProductIds: string[],
  ): Promise<number> {
    const result = await this.prisma.driverCityProduct.deleteMany({
      where: {
        userProfileId,
        driverVehicleId,
        cityProductId: { in: cityProductIds },
      },
    });

    return result.count;
  }

  /**
   * Find all city products for a driver vehicle.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   */
  async findDriverCityProductsByDriverVehicle(
    userProfileId: string,
    driverVehicleId: string,
  ): Promise<DriverCityProduct[]> {
    return this.findMany({
      where: {
        userProfileId,
        driverVehicleId,
      },
      include: {
        cityProduct: {
          include: {
            city: {
              select: { id: true, name: true, state: true, country: true },
            },
            product: {
              select: { id: true, name: true, description: true, icon: true },
            },
            vehicleType: {
              select: { id: true, name: true, description: true },
            },
          },
        },
        userProfile: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });
  }

  /**
   * Paginate city products for a driver vehicle.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   * @param page - Page number
   * @param limit - Items per page
   */
  async paginateDriverVehicleCityProducts(
    userProfileId: string,
    driverVehicleId: string,
    page = 1,
    limit = 10,
  ) {
    const whereClause = {
      userProfileId,
      driverVehicleId,
    };

    const include = {
      cityProduct: {
        include: {
          city: {
            select: { id: true, name: true, state: true, country: true },
          },
          product: {
            select: { id: true, name: true, description: true, icon: true },
          },
          vehicleType: {
            select: { id: true, name: true, description: true },
          },
        },
      },
    };

    return this.paginate(page, limit, { where: whereClause, include });
  }

  /**
   * Update primary status of a city product for a driver vehicle.
   * @param userProfileId - Driver's user profile ID
   * @param driverVehicleId - Driver vehicle ID
   * @param cityProductId - City product ID
   * @param isPrimary - Primary status
   */
  async updateCityProductPrimaryStatus(
    userProfileId: string,
    driverVehicleId: string,
    cityProductId: string,
    isPrimary: boolean,
  ): Promise<DriverCityProduct> {
    const updated = await this.prisma.driverCityProduct.updateMany({
      where: {
        userProfileId,
        driverVehicleId,
        cityProductId,
      },
      data: {
        isPrimary,
      },
    });

    if (updated.count === 0) {
      throw new Error('Driver city product relationship not found');
    }

    // Return the updated record
    const result = await this.findOne({
      where: {
        userProfileId,
        driverVehicleId,
        cityProductId,
      },
      include: {
        cityProduct: {
          include: {
            city: true,
            product: true,
          },
        },
      },
    });

    if (!result) {
      throw new Error('Updated driver city product not found');
    }

    return result;
  }
}
