import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository } from './base.repository';
import { Charge } from './models/charge.model';

@Injectable()
export class ChargeRepository extends BaseRepository<Charge> {
  protected readonly modelName = 'charge';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new charge record.
   */
  async createCharge(
    data: Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Charge> {
    return this.create(data);
  }

  /**
   * Find all charges by charge group ID.
   */
  async findChargesByChargeGroupId(chargeGroupId: string): Promise<Charge[]> {
    return this.findMany({
      where: { chargeGroupId },
      include: {
        percentageOfCharge: true,
      },
    });
  }

  async findChargeById(id: string): Promise<Charge | null> {
    // First find the charge
    const charge = await this.findOne({
      where: { id },
      include: {
        percentageOfCharge: true,
      },
    });

    if (!charge) {
      return null;
    }
    return charge;
  }

  /**
   * Find charge by identifier.
   */
  async findChargeByIdentifier(identifier: string): Promise<Charge | null> {
    return this.findOne({
      where: { identifier },
    });
  }

  /**
   * Update charge by ID and charge group ID.
   * Note: Since charges can now exist independently, this method updates the charge
   * regardless of its group attachments. The chargeGroupId parameter is kept for
   * backward compatibility but is no longer used as a strict filter.
   */
  async updateChargeById(
    id: string,
    data: Partial<Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>,
  ): Promise<Charge> {
    return this.update({
      where: { id },
      data,
      include: {
        percentageOfCharge: true,
      },
    });
  }

  /**
   * Check if identifier exists (across all charges).
   */
  async identifierExistsGlobally(
    identifier: string | undefined,
    excludeId?: string,
  ): Promise<boolean> {
    const where: any = {
      identifier: {
        equals: identifier,
        mode: 'insensitive',
      },
    };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const charge = await this.findOne({ where });
    return !!charge;
  }

  /**
   * Check if identifier exists (legacy method for backward compatibility).
   */
  async identifierExists(
    identifier: string | undefined,
    chargeGroupId?: string,
    excludeId?: string,
  ): Promise<boolean> {
    // If chargeGroupId is provided, use the old logic for backward compatibility
    if (chargeGroupId) {
      const where: any = {
        identifier: {
          equals: identifier,
          mode: 'insensitive',
        },
        chargeGroupId,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const charge = await this.findOne({ where });
      return !!charge;
    }

    // Otherwise, check globally (new behavior)
    return this.identifierExistsGlobally(identifier, excludeId);
  }

  /**
   * Find charges that reference this charge as percentage base.
   */
  async findChargesReferencingAsPercentage(
    chargeId: string,
  ): Promise<Charge[]> {
    return this.findMany({
      where: { percentageOfChargeId: chargeId },
    });
  }
}
